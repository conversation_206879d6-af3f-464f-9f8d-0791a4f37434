"""
Simple Telegram Bot using direct API calls (Python 3.13 compatible)
"""
import asyncio
import aiohttp
import json
from config import get_settings
from exchanges.manager import ExchangeManager
from strategies.manager import StrategyManager
from analysis.market_analyzer import MarketAnalyzer
from loguru import logger

class SimpleTelegramBot:
    """Simple Telegram bot using direct API calls"""
    
    def __init__(self):
        self.settings = get_settings()
        self.bot_token = self.settings.telegram_bot_token
        self.admin_user_id = self.settings.telegram_admin_user_id
        self.base_url = f"https://api.telegram.org/bot{self.bot_token}"
        
        self.exchange_manager = None
        self.strategy_manager = None
        self.market_analyzer = None
        self.session = None
        self.running = False
    
    async def send_message(self, chat_id: int, text: str, parse_mode: str = "Markdown"):
        """Send message to Telegram"""
        try:
            url = f"{self.base_url}/sendMessage"
            data = {
                "chat_id": chat_id,
                "text": text,
                "parse_mode": parse_mode
            }
            
            async with self.session.post(url, json=data) as response:
                return await response.json()
                
        except Exception as e:
            logger.error(f"Error sending message: {e}")
            return None
    
    async def get_updates(self, offset: int = 0):
        """Get updates from Telegram"""
        try:
            url = f"{self.base_url}/getUpdates"
            params = {"offset": offset, "timeout": 30}
            
            async with self.session.get(url, params=params) as response:
                return await response.json()
                
        except Exception as e:
            logger.error(f"Error getting updates: {e}")
            return None
    
    def is_authorized(self, user_id: int) -> bool:
        """Check if user is authorized"""
        return user_id == self.admin_user_id
    
    async def handle_start(self, chat_id: int, user_id: int):
        """Handle /start command"""
        if not self.is_authorized(user_id):
            await self.send_message(chat_id, "❌ Je bent niet geautoriseerd om deze bot te gebruiken.")
            return
        
        welcome_message = """
🤖 **Welkom bij de Trading Bot!**

**Beschikbare commando's:**
/start - Start de bot
/balance - Toon portfolio balances
/price BTC/USDT - Toon prijs van een coin
/analysis - Toon marktanalyse
/strategies - Toon trading strategieën
/start_trading - Start automatische trading
/stop_trading - Stop automatische trading
/help - Toon deze help

**Voorbeelden:**
• /price BTC/USDT - Bitcoin prijs
• /analysis - Marktanalyse rapport

⚠️ **Let op:** Alle trading acties zijn echt! Wees voorzichtig.
        """
        
        await self.send_message(chat_id, welcome_message)
    
    async def handle_price(self, chat_id: int, user_id: int, args: list):
        """Handle /price command"""
        if not self.is_authorized(user_id):
            await self.send_message(chat_id, "❌ Niet geautoriseerd.")
            return
        
        if not args:
            await self.send_message(chat_id, "❌ Gebruik: /price <symbol>\nVoorbeeld: /price BTC/USDT")
            return
        
        symbol = args[0].upper()
        await self.send_message(chat_id, f"📊 Prijzen ophalen voor {symbol}...")
        
        try:
            tickers = await self.exchange_manager.get_ticker_from_all(symbol)
            
            if not tickers:
                await self.send_message(chat_id, f"❌ Geen prijsdata gevonden voor {symbol}")
                return
            
            message = f"📊 **Prijzen voor {symbol}**\n\n"
            
            for exchange_name, ticker in tickers.items():
                message += f"**{exchange_name.upper()}:**\n"
                message += f"  Last: ${ticker.last:.8f}\n"
                message += f"  Bid: ${ticker.bid:.8f}\n"
                message += f"  Ask: ${ticker.ask:.8f}\n"
                message += f"  24h High: ${ticker.high:.8f}\n"
                message += f"  24h Low: ${ticker.low:.8f}\n\n"
            
            await self.send_message(chat_id, message)
            
        except Exception as e:
            logger.error(f"Error fetching price: {e}")
            await self.send_message(chat_id, f"❌ Fout bij ophalen prijs: {str(e)}")
    
    async def handle_balance(self, chat_id: int, user_id: int):
        """Handle /balance command"""
        if not self.is_authorized(user_id):
            await self.send_message(chat_id, "❌ Niet geautoriseerd.")
            return
        
        await self.send_message(chat_id, "💰 Balances ophalen...")
        
        try:
            all_balances = await self.exchange_manager.get_all_balances()
            
            message = "💰 **Portfolio Balances**\n\n"
            
            for exchange_name, balances in all_balances.items():
                message += f"**{exchange_name.upper()}:**\n"
                
                if not balances:
                    message += "  Geen balances gevonden\n\n"
                    continue
                
                non_zero_balances = {k: v for k, v in balances.items() if v.total > 0}
                
                if not non_zero_balances:
                    message += "  Geen balances > 0\n\n"
                    continue
                
                for currency, balance in sorted(non_zero_balances.items()):
                    message += f"  {currency}: {balance.total:.8f}\n"
                
                message += "\n"
            
            await self.send_message(chat_id, message)
            
        except Exception as e:
            logger.error(f"Error fetching balances: {e}")
            await self.send_message(chat_id, f"❌ Fout bij ophalen balances: {str(e)}")
    
    async def handle_analysis(self, chat_id: int, user_id: int):
        """Handle /analysis command"""
        if not self.is_authorized(user_id):
            await self.send_message(chat_id, "❌ Niet geautoriseerd.")
            return
        
        await self.send_message(chat_id, "📊 Marktanalyse ophalen...")
        
        try:
            # Get or generate analysis
            latest = self.market_analyzer.get_latest_analysis()
            if not latest:
                await self.market_analyzer._perform_market_analysis()
                latest = self.market_analyzer.get_latest_analysis()
            
            if latest:
                message = "📈 **Marktanalyse**\n\n"
                
                for symbol, analysis in latest.items():
                    market_data = analysis.get('market_data', {})
                    alerts = analysis.get('alerts', [])
                    
                    price_change = market_data.get('price_change_24h', 0)
                    change_emoji = "📈" if price_change > 0 else "📉" if price_change < 0 else "➡️"
                    
                    message += f"{change_emoji} **{symbol}**\n"
                    message += f"  Prijs: ${market_data.get('price', 0):.2f}\n"
                    message += f"  24h: {price_change:+.2f}%\n"
                    message += f"  Alerts: {len(alerts)}\n\n"
                
                await self.send_message(chat_id, message)
            else:
                await self.send_message(chat_id, "❌ Geen analysedata beschikbaar.")
                
        except Exception as e:
            logger.error(f"Error getting analysis: {e}")
            await self.send_message(chat_id, f"❌ Fout bij ophalen analyse: {str(e)}")
    
    async def handle_strategies(self, chat_id: int, user_id: int):
        """Handle /strategies command"""
        if not self.is_authorized(user_id):
            await self.send_message(chat_id, "❌ Niet geautoriseerd.")
            return
        
        try:
            status = self.strategy_manager.get_strategy_status()
            
            message = "🤖 **Trading Strategieën**\n\n"
            
            for name, info in status.items():
                status_emoji = "✅" if info['enabled'] else "❌"
                active_emoji = "🟢" if info['active'] else "🔴"
                
                message += f"{status_emoji} **{name}**\n"
                message += f"   Status: {active_emoji} {'Actief' if info['active'] else 'Inactief'}\n"
                message += f"   Posities: {info['positions']}\n"
                message += f"   Timeframe: {info['timeframe']}\n"
                message += f"   Risico: {info['risk_percentage']}%\n\n"
            
            await self.send_message(chat_id, message)
            
        except Exception as e:
            logger.error(f"Error getting strategies: {e}")
            await self.send_message(chat_id, f"❌ Fout bij ophalen strategieën: {str(e)}")
    
    async def handle_message(self, message: dict):
        """Handle incoming message"""
        try:
            chat_id = message['chat']['id']
            user_id = message['from']['id']
            text = message.get('text', '')
            
            if not text.startswith('/'):
                return
            
            # Parse command and arguments
            parts = text.split()
            command = parts[0].lower()
            args = parts[1:] if len(parts) > 1 else []
            
            # Handle commands
            if command in ['/start', '/help']:
                await self.handle_start(chat_id, user_id)
            elif command == '/price':
                await self.handle_price(chat_id, user_id, args)
            elif command == '/balance':
                await self.handle_balance(chat_id, user_id)
            elif command == '/analysis':
                await self.handle_analysis(chat_id, user_id)
            elif command == '/strategies':
                await self.handle_strategies(chat_id, user_id)
            elif command == '/start_trading':
                if self.is_authorized(user_id):
                    await self.send_message(chat_id, "🤖 Automatische trading gestart!")
                    asyncio.create_task(self.strategy_manager.start_automated_trading())
            elif command == '/stop_trading':
                if self.is_authorized(user_id):
                    self.strategy_manager.stop_automated_trading()
                    await self.send_message(chat_id, "🛑 Automatische trading gestopt!")
            else:
                if self.is_authorized(user_id):
                    await self.send_message(chat_id, "❌ Onbekend commando. Gebruik /help voor hulp.")
                    
        except Exception as e:
            logger.error(f"Error handling message: {e}")
    
    async def initialize(self):
        """Initialize all components"""
        logger.info("🚀 Initializing Simple Telegram Bot...")
        
        # Validate settings
        if not self.settings.validate():
            logger.error("❌ Invalid configuration. Please check your .env file.")
            return False
        
        if not self.bot_token:
            logger.error("❌ No Telegram bot token found.")
            return False
        
        # Initialize HTTP session
        self.session = aiohttp.ClientSession()
        
        # Test bot token
        try:
            url = f"{self.base_url}/getMe"
            async with self.session.get(url) as response:
                result = await response.json()
                if result.get('ok'):
                    bot_info = result['result']
                    logger.info(f"✅ Bot connected: @{bot_info['username']}")
                else:
                    logger.error("❌ Invalid bot token")
                    return False
        except Exception as e:
            logger.error(f"❌ Error testing bot token: {e}")
            return False
        
        # Initialize exchange manager
        self.exchange_manager = ExchangeManager()
        
        # Connect to exchanges
        logger.info("Connecting to exchanges...")
        connection_results = await self.exchange_manager.connect_all()
        
        for exchange, connected in connection_results.items():
            if connected:
                logger.info(f"✅ Connected to {exchange}")
            else:
                logger.error(f"❌ Failed to connect to {exchange}")
        
        if not any(connection_results.values()):
            logger.error("No exchanges connected. Exiting...")
            return False
        
        # Initialize strategy manager
        self.strategy_manager = StrategyManager(self.exchange_manager)
        
        # Initialize market analyzer
        self.market_analyzer = MarketAnalyzer(self.exchange_manager)
        
        logger.info("✅ All components initialized")
        return True
    
    async def run(self):
        """Run the bot"""
        if not await self.initialize():
            return
        
        self.running = True
        offset = 0
        
        # Start market analyzer
        market_task = asyncio.create_task(self.market_analyzer.start_analysis())
        
        logger.info("🚀 Telegram bot is running...")
        logger.info("Send /start to your bot to begin!")
        
        try:
            while self.running:
                # Get updates
                updates = await self.get_updates(offset)
                
                if updates and updates.get('ok'):
                    for update in updates['result']:
                        if 'message' in update:
                            await self.handle_message(update['message'])
                        
                        # Update offset
                        offset = update['update_id'] + 1
                
                await asyncio.sleep(1)  # Small delay
                
        except KeyboardInterrupt:
            logger.info("Bot stopped by user")
        except Exception as e:
            logger.error(f"Error in bot loop: {e}")
        finally:
            # Cleanup
            self.running = False
            self.market_analyzer.stop_analysis()
            self.strategy_manager.stop_automated_trading()
            
            if self.session:
                await self.session.close()
            
            await market_task

async def main():
    """Main function"""
    bot = SimpleTelegramBot()
    await bot.run()

if __name__ == "__main__":
    asyncio.run(main())
