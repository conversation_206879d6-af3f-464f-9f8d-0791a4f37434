# Telegram Trading Bot

Een Telegram bot voor trading op KuCoin en MEXC exchanges.

## Features

- 📱 Telegram interface voor eenvoudige bediening
- 🔄 Ondersteuning voor KuCoin en MEXC exchanges
- 💰 Portfolio overzicht
- 📊 Real-time prijzen en balances
- 🛒 Order plaatsing (buy/sell)
- 🔔 Price alerts
- 🔐 Veilige API key opslag

## Setup

### 1. Virtual Environment

```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# of
venv\Scripts\activate     # Windows
```

### 2. Dependencies

```bash
pip install -r requirements.txt
```

### 3. Configuration

1. Kopieer `.env.example` naar `.env`
2. Vul je API keys en tokens in
3. Configureer je Telegram bot token

### 4. Telegram Bot Setup

1. Ga naar [@BotFather](https://t.me/botfather) op Telegram
2. Maak een nieuwe bot met `/newbot`
3. <PERSON><PERSON><PERSON> de bot token naar je `.env` file
4. Vind je Telegram User ID en voeg toe aan `.env`

### 5. Exchange API Setup

#### KuCoin
1. Ga naar KuCoin API Management
2. Maak nieuwe API key met trading permissions
3. Voeg credentials toe aan `.env`

#### MEXC
1. Ga naar MEXC API Management
2. Maak nieuwe API key met trading permissions
3. Voeg credentials toe aan `.env`

## Usage

```bash
python main.py
```

## Commands

- `/start` - Start de bot
- `/balance` - Toon portfolio balances
- `/price <symbol>` - Toon prijs van een coin
- `/buy <symbol> <amount>` - Koop order plaatsen
- `/sell <symbol> <amount>` - Verkoop order plaatsen
- `/orders` - Toon open orders
- `/help` - Toon alle commands

## Security

- API keys worden versleuteld opgeslagen
- Alleen geautoriseerde gebruikers kunnen de bot gebruiken
- Alle trading acties worden gelogd

## Disclaimer

⚠️ **Risico Waarschuwing**: Trading in cryptocurrencies is risicovol. Gebruik deze bot op eigen risico.
