# Telegram Bot
python-telegram-bot==20.7

# Exchange APIs
ccxt==4.2.25

# Environment variables
python-dotenv==1.0.0

# Async support
aiohttp==3.9.1

# Logging
loguru==0.7.2

# Data handling
pandas==2.1.4
numpy==1.26.2

# Configuration
pydantic==2.5.2
pydantic-settings==2.1.0

# Security
cryptography==41.0.8

# Technical Analysis
TA-Lib==0.4.28
pandas==2.1.4
numpy==1.26.2

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
