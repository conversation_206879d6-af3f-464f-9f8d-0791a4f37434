"""
Configuration management for the Telegram Trading Bot (simplified for Python 3.13)
"""
import os
from typing import Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Settings:
    """Application settings (simplified without pydantic)"""

    def __init__(self):
        # Telegram Configuration
        self.telegram_bot_token = os.getenv("TELEGRAM_BOT_TOKEN", "")

        # Parse multiple admin user IDs (comma-separated)
        admin_ids_str = os.getenv("TELEGRAM_ADMIN_USER_ID", "0")
        if "," in admin_ids_str:
            self.telegram_admin_user_ids = [int(id.strip()) for id in admin_ids_str.split(",") if id.strip()]
        else:
            self.telegram_admin_user_ids = [int(admin_ids_str)] if admin_ids_str != "0" else []

        # Keep backward compatibility
        self.telegram_admin_user_id = self.telegram_admin_user_ids[0] if self.telegram_admin_user_ids else 0

        # KuCoin Configuration
        self.kucoin_api_key = os.getenv("KUCOIN_API_KEY", "")
        self.kucoin_secret_key = os.getenv("KUCOIN_SECRET_KEY", "")
        self.kucoin_passphrase = os.getenv("KUCOIN_PASSPHRASE", "")
        self.kucoin_sandbox = os.getenv("KUCOIN_SANDBOX", "false").lower() == "true"

        # MEXC Configuration
        self.mexc_api_key = os.getenv("MEXC_API_KEY", "")
        self.mexc_secret_key = os.getenv("MEXC_SECRET_KEY", "")
        self.mexc_sandbox = os.getenv("MEXC_SANDBOX", "false").lower() == "true"

        # Database Configuration
        self.database_url = os.getenv("DATABASE_URL", "sqlite:///trading_bot.db")

        # Logging Configuration
        self.log_level = os.getenv("LOG_LEVEL", "INFO")
        self.log_file = os.getenv("LOG_FILE", "trading_bot.log")

        # Security
        self.encryption_key = os.getenv("ENCRYPTION_KEY")

    def validate(self) -> bool:
        """Validate required settings"""
        required_fields = [
            ("telegram_bot_token", self.telegram_bot_token),
            ("telegram_admin_user_id", self.telegram_admin_user_id),
            ("kucoin_api_key", self.kucoin_api_key),
            ("kucoin_secret_key", self.kucoin_secret_key),
            ("kucoin_passphrase", self.kucoin_passphrase),
            ("mexc_api_key", self.mexc_api_key),
            ("mexc_secret_key", self.mexc_secret_key),
        ]

        missing_fields = []
        for field_name, field_value in required_fields:
            if not field_value or (isinstance(field_value, int) and field_value == 0):
                missing_fields.append(field_name)

        if missing_fields:
            print(f"❌ Missing required configuration: {', '.join(missing_fields)}")
            return False

        return True

    def is_authorized_user(self, user_id: int) -> bool:
        """Check if user ID is authorized"""
        return user_id in self.telegram_admin_user_ids

# Global settings instance
settings = Settings()

def get_settings() -> Settings:
    """Get application settings"""
    return settings
