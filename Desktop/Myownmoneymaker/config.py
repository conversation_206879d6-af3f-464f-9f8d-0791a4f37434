"""
Configuration management for the Telegram Trading Bot
"""
import os
from typing import Optional
from pydantic import BaseSettings, Field
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Settings(BaseSettings):
    """Application settings"""
    
    # Telegram Configuration
    telegram_bot_token: str = Field(..., env="TELEGRAM_BOT_TOKEN")
    telegram_admin_user_id: int = Field(..., env="TELEGRAM_ADMIN_USER_ID")
    
    # KuCoin Configuration
    kucoin_api_key: str = Field(..., env="KUCOIN_API_KEY")
    kucoin_secret_key: str = Field(..., env="KUCOIN_SECRET_KEY")
    kucoin_passphrase: str = Field(..., env="KUCOIN_PASSPHRASE")
    kucoin_sandbox: bool = Field(False, env="KUCOIN_SANDBOX")
    
    # MEXC Configuration
    mexc_api_key: str = Field(..., env="MEXC_API_KEY")
    mexc_secret_key: str = Field(..., env="MEXC_SECRET_KEY")
    mexc_sandbox: bool = Field(False, env="MEXC_SANDBOX")
    
    # Database Configuration
    database_url: str = Field("sqlite:///trading_bot.db", env="DATABASE_URL")
    
    # Logging Configuration
    log_level: str = Field("INFO", env="LOG_LEVEL")
    log_file: str = Field("trading_bot.log", env="LOG_FILE")
    
    # Security
    encryption_key: Optional[str] = Field(None, env="ENCRYPTION_KEY")
    
    class Config:
        env_file = ".env"
        case_sensitive = False

# Global settings instance
settings = Settings()

def get_settings() -> Settings:
    """Get application settings"""
    return settings
