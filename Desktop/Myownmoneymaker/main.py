"""
Main entry point for the Telegram Trading Bot
"""
import asyncio
import sys
from telegram.ext import Application, CommandHandler, CallbackQueryHandler
from loguru import logger
from config import get_settings
from exchanges.manager import ExchangeManager
from bot.handlers import TradingBotHandlers
from bot.callbacks import CallbackHandlers

async def main():
    """Main function to start the bot"""
    
    # Setup logging
    settings = get_settings()
    logger.remove()
    logger.add(
        sys.stderr,
        level=settings.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    logger.add(
        settings.log_file,
        level=settings.log_level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="10 MB"
    )
    
    logger.info("Starting Telegram Trading Bot...")
    
    try:
        # Initialize exchange manager
        exchange_manager = ExchangeManager()
        
        # Connect to exchanges
        logger.info("Connecting to exchanges...")
        connection_results = await exchange_manager.connect_all()
        
        for exchange, connected in connection_results.items():
            if connected:
                logger.info(f"✅ Connected to {exchange}")
            else:
                logger.error(f"❌ Failed to connect to {exchange}")
        
        # Check if at least one exchange is connected
        if not any(connection_results.values()):
            logger.error("No exchanges connected. Exiting...")
            return
        
        # Initialize handlers
        handlers = TradingBotHandlers(exchange_manager)
        callback_handlers = CallbackHandlers(exchange_manager)
        
        # Create application
        application = Application.builder().token(settings.telegram_bot_token).build()
        
        # Add command handlers
        application.add_handler(CommandHandler("start", handlers.start))
        application.add_handler(CommandHandler("help", handlers.help_command))
        application.add_handler(CommandHandler("balance", handlers.balance))
        application.add_handler(CommandHandler("price", handlers.price))
        application.add_handler(CommandHandler("buy", handlers.buy))
        application.add_handler(CommandHandler("sell", handlers.sell))
        application.add_handler(CommandHandler("orders", handlers.orders))
        application.add_handler(CommandHandler("cancel", handlers.cancel))
        application.add_handler(CommandHandler("exchanges", handlers.exchanges))
        application.add_handler(CommandHandler("bestprice", handlers.best_price))
        
        # Add callback handler
        application.add_handler(CallbackQueryHandler(callback_handlers.button_callback))
        
        logger.info("Bot handlers registered")
        
        # Start the bot
        logger.info("Starting bot polling...")
        await application.run_polling(allowed_updates=["message", "callback_query"])
        
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Error starting bot: {e}")
        raise

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Bot shutdown complete")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)
