"""
Momentum Breakout Strategy - Trend following with breakout detection
"""
import asyncio
from decimal import Decimal
from typing import Dict, List
import numpy as np
from .base import BaseStrategy, TradingSignal, SignalType, Position, StrategyConfig
from analysis.technical import TechnicalAnalyzer
from analysis.ai_analyzer import AIAnalyzer
from loguru import logger

class MomentumBreakoutStrategy(BaseStrategy):
    """
    Momentum breakout strategy that identifies and trades strong trends
    
    Features:
    - Breakout detection from consolidation patterns
    - Volume confirmation
    - Trend strength analysis
    - Dynamic stop losses based on volatility
    - Position scaling on strong momentum
    - Multi-timeframe analysis
    """
    
    def __init__(self, config: StrategyConfig):
        super().__init__(config)
        self.technical_analyzer = TechnicalAnalyzer()
        self.ai_analyzer = AIAnalyzer()
        
        # Breakout specific settings
        self.consolidation_threshold = Decimal('2')  # 2% range for consolidation
        self.breakout_volume_multiplier = 1.5  # Volume must be 1.5x average
        self.trend_strength_threshold = 0.7
        self.atr_multiplier = 2.0  # ATR multiplier for stop loss
        self.momentum_lookback = 20  # Periods for momentum calculation
        
    async def analyze(self, symbol: str, market_data: Dict) -> TradingSignal:
        """Analyze market for momentum breakout opportunities"""
        try:
            # Multi-timeframe analysis
            tf_1h = await self.technical_analyzer.analyze(symbol, market_data, "1h")
            tf_4h = await self.technical_analyzer.analyze(symbol, market_data, "4h")
            tf_1d = await self.technical_analyzer.analyze(symbol, market_data, "1d")
            
            # AI trend analysis
            ai_analysis = await self.ai_analyzer.analyze_trend(symbol, market_data)
            
            current_price = Decimal(str(market_data['price']))
            
            # Breakout detection
            breakout_signal = await self._detect_breakout(market_data, tf_1h)
            
            # Volume confirmation
            volume_confirmation = await self._confirm_volume_breakout(market_data)
            
            # Trend strength across timeframes
            trend_strength = await self._analyze_trend_strength(tf_1h, tf_4h, tf_1d)
            
            # Momentum analysis
            momentum_score = await self._calculate_momentum(market_data, tf_1h)
            
            # Support/Resistance levels
            key_levels = await self._identify_key_levels(market_data)
            
            # Combine all signals
            final_score = self._combine_breakout_signals(
                breakout_signal, volume_confirmation, trend_strength, 
                momentum_score, ai_analysis, key_levels
            )
            
            # Generate trading signal
            if final_score > self.config.min_confidence:
                signal_type = SignalType.BUY
                confidence = float(final_score)
                
                # Dynamic stop loss based on ATR
                atr = tf_1h.get('atr', current_price * Decimal('0.02'))
                stop_loss = current_price - (Decimal(str(atr)) * Decimal(str(self.atr_multiplier)))
                
                # Take profit based on risk-reward ratio (1:3)
                risk = current_price - stop_loss
                take_profit = current_price + (risk * 3)
                
                reasoning = f"Momentum breakout BUY: Breakout={breakout_signal:.2f}, Volume={volume_confirmation:.2f}, Trend={trend_strength:.2f}, Momentum={momentum_score:.2f}"
                
            elif final_score < -self.config.min_confidence:
                signal_type = SignalType.SELL
                confidence = float(abs(final_score))
                
                atr = tf_1h.get('atr', current_price * Decimal('0.02'))
                stop_loss = current_price + (Decimal(str(atr)) * Decimal(str(self.atr_multiplier)))
                
                risk = stop_loss - current_price
                take_profit = current_price - (risk * 3)
                
                reasoning = f"Momentum breakout SELL: Breakout={breakout_signal:.2f}, Volume={volume_confirmation:.2f}, Trend={trend_strength:.2f}"
                
            else:
                signal_type = SignalType.HOLD
                confidence = 0.5
                stop_loss = None
                take_profit = None
                reasoning = "No clear breakout signal"
            
            return TradingSignal(
                signal=signal_type,
                confidence=confidence,
                price=current_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                reasoning=reasoning
            )
            
        except Exception as e:
            logger.error(f"Error in momentum breakout analysis for {symbol}: {e}")
            return TradingSignal(
                signal=SignalType.HOLD,
                confidence=0.0,
                price=Decimal(str(market_data['price'])),
                reasoning=f"Breakout analysis error: {str(e)}"
            )
    
    async def _detect_breakout(self, market_data: Dict, technical_data: Dict) -> float:
        """Detect breakout from consolidation patterns"""
        try:
            # Get recent price data
            high_20 = technical_data.get('high_20', market_data['price'])
            low_20 = technical_data.get('low_20', market_data['price'])
            current_price = Decimal(str(market_data['price']))
            
            # Calculate consolidation range
            if high_20 > low_20:
                range_size = (high_20 - low_20) / low_20 * 100
                
                # Check if we're in consolidation
                if range_size < float(self.consolidation_threshold):
                    # We're in consolidation, check for breakout
                    if current_price > Decimal(str(high_20)):
                        return 0.8  # Bullish breakout
                    elif current_price < Decimal(str(low_20)):
                        return -0.8  # Bearish breakout
                    else:
                        return 0.0  # Still in range
                else:
                    # Already trending, check momentum continuation
                    price_change = technical_data.get('price_change_24h', 0)
                    if abs(price_change) > 5:  # Strong momentum
                        return 0.6 if price_change > 0 else -0.6
            
            return 0.0
            
        except Exception:
            return 0.0
    
    async def _confirm_volume_breakout(self, market_data: Dict) -> float:
        """Confirm breakout with volume analysis"""
        try:
            current_volume = market_data.get('volume', 0)
            avg_volume = market_data.get('avg_volume_20', current_volume)
            
            if avg_volume == 0:
                return 0.0
            
            volume_ratio = current_volume / avg_volume
            
            # Volume confirmation levels
            if volume_ratio > self.breakout_volume_multiplier:
                return min(volume_ratio / 3, 1.0)  # Strong volume confirmation
            elif volume_ratio > 1.0:
                return 0.3  # Moderate volume
            else:
                return -0.3  # Weak volume
                
        except Exception:
            return 0.0
    
    async def _analyze_trend_strength(self, tf_1h: Dict, tf_4h: Dict, tf_1d: Dict) -> float:
        """Analyze trend strength across multiple timeframes"""
        try:
            timeframes = [tf_1h, tf_4h, tf_1d]
            weights = [0.5, 0.3, 0.2]  # Higher weight for shorter timeframes
            
            trend_scores = []
            
            for tf in timeframes:
                # EMA alignment
                ema_12 = tf.get('ema_12', 0)
                ema_26 = tf.get('ema_26', 0)
                ema_50 = tf.get('ema_50', 0)
                current_price = tf.get('close', 0)
                
                score = 0.0
                
                # Check EMA alignment
                if current_price > ema_12 > ema_26 > ema_50:
                    score += 0.4  # Strong bullish alignment
                elif current_price < ema_12 < ema_26 < ema_50:
                    score -= 0.4  # Strong bearish alignment
                
                # MACD trend
                macd = tf.get('macd', 0)
                macd_signal = tf.get('macd_signal', 0)
                if macd > macd_signal and macd > 0:
                    score += 0.3
                elif macd < macd_signal and macd < 0:
                    score -= 0.3
                
                # ADX trend strength
                adx = tf.get('adx', 0)
                if adx > 25:  # Strong trend
                    score += 0.3
                
                trend_scores.append(score)
            
            # Weighted average
            weighted_score = sum(score * weight for score, weight in zip(trend_scores, weights))
            return max(-1.0, min(1.0, weighted_score))
            
        except Exception:
            return 0.0
    
    async def _calculate_momentum(self, market_data: Dict, technical_data: Dict) -> float:
        """Calculate momentum score"""
        try:
            # Rate of change
            roc = technical_data.get('roc_14', 0)
            
            # RSI momentum
            rsi = technical_data.get('rsi', 50)
            
            # Price velocity
            price_changes = [
                market_data.get('price_change_1h', 0),
                market_data.get('price_change_4h', 0),
                market_data.get('price_change_24h', 0)
            ]
            
            momentum_score = 0.0
            
            # ROC momentum
            if abs(roc) > 5:  # Strong momentum
                momentum_score += 0.4 if roc > 0 else -0.4
            
            # RSI momentum (avoid extremes)
            if 40 < rsi < 60:
                momentum_score += 0.2 if rsi > 50 else -0.2
            elif rsi > 70:
                momentum_score -= 0.2  # Overbought
            elif rsi < 30:
                momentum_score += 0.2  # Oversold
            
            # Price velocity consistency
            positive_changes = sum(1 for change in price_changes if change > 0)
            if positive_changes == 3:
                momentum_score += 0.4
            elif positive_changes == 0:
                momentum_score -= 0.4
            
            return max(-1.0, min(1.0, momentum_score))
            
        except Exception:
            return 0.0
    
    async def _identify_key_levels(self, market_data: Dict) -> Dict:
        """Identify key support and resistance levels"""
        try:
            # Simplified key level identification
            high_24h = market_data.get('high_24h', 0)
            low_24h = market_data.get('low_24h', 0)
            high_7d = market_data.get('high_7d', high_24h)
            low_7d = market_data.get('low_7d', low_24h)
            
            current_price = Decimal(str(market_data['price']))
            
            levels = {
                'resistance_near': Decimal(str(high_24h)),
                'resistance_major': Decimal(str(high_7d)),
                'support_near': Decimal(str(low_24h)),
                'support_major': Decimal(str(low_7d))
            }
            
            # Calculate distance to levels
            distances = {}
            for level_name, level_price in levels.items():
                if level_price > 0:
                    distance = abs(current_price - level_price) / current_price
                    distances[level_name] = float(distance)
            
            return distances
            
        except Exception:
            return {}
    
    def _combine_breakout_signals(self, breakout: float, volume: float, trend: float, 
                                 momentum: float, ai_analysis: Dict, levels: Dict) -> float:
        """Combine all breakout signals"""
        
        weights = {
            'breakout': 0.3,
            'volume': 0.2,
            'trend': 0.25,
            'momentum': 0.15,
            'ai': 0.1
        }
        
        # Get AI score
        ai_score = (ai_analysis.get('trend_strength', 0.5) - 0.5) * 2
        
        # Combine weighted signals
        combined = (
            weights['breakout'] * breakout +
            weights['volume'] * volume +
            weights['trend'] * trend +
            weights['momentum'] * momentum +
            weights['ai'] * ai_score
        )
        
        # Adjust based on proximity to key levels
        if levels:
            # Boost signal if breaking major resistance/support
            resistance_distance = levels.get('resistance_major', 1.0)
            support_distance = levels.get('support_major', 1.0)
            
            if resistance_distance < 0.02 and combined > 0:  # Near resistance, bullish
                combined += 0.1
            elif support_distance < 0.02 and combined < 0:  # Near support, bearish
                combined -= 0.1
        
        return max(-1.0, min(1.0, combined))
    
    async def should_enter_position(self, signal: TradingSignal, portfolio_value: Decimal) -> bool:
        """Determine if we should enter a breakout position"""
        
        # Check confidence threshold
        if signal.confidence < float(self.config.min_confidence):
            return False
        
        # Limit positions
        if len(self.positions) >= self.config.max_positions:
            return False
        
        # Only enter on strong signals
        if signal.signal in [SignalType.BUY, SignalType.SELL] and signal.confidence > 0.7:
            return True
        
        return False
    
    async def should_exit_position(self, position: Position, current_price: Decimal) -> bool:
        """Determine if we should exit a breakout position"""
        
        # Update trailing stop based on ATR
        if position.trailing_stop:
            new_stop = await self.update_trailing_stop(position, current_price)
            if new_stop:
                position.stop_loss = new_stop
        
        # Check stop loss
        if position.stop_loss:
            if position.side == 'long' and current_price <= position.stop_loss:
                return True
            elif position.side == 'short' and current_price >= position.stop_loss:
                return True
        
        # Check take profit
        if position.take_profit:
            if position.side == 'long' and current_price >= position.take_profit:
                return True
            elif position.side == 'short' and current_price <= position.take_profit:
                return True
        
        return False
