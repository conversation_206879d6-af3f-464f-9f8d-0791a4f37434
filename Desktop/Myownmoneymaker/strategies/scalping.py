"""
Scalping Strategy - High-frequency short-term trading
"""
import asyncio
from decimal import Decimal
from typing import Dict, List
import time
from .base import BaseStrategy, TradingSignal, SignalType, Position, StrategyConfig
from analysis.technical import TechnicalAnalyzer
from loguru import logger

class ScalpingStrategy(BaseStrategy):
    """
    High-frequency scalping strategy
    
    Features:
    - 1-5 minute timeframes
    - Quick entry/exit (30 seconds to 5 minutes)
    - Small profit targets (0.1-0.5%)
    - Tight stop losses (0.1-0.2%)
    - High volume requirements
    - Order book analysis
    """
    
    def __init__(self, config: StrategyConfig):
        super().__init__(config)
        self.technical_analyzer = TechnicalAnalyzer()
        
        # Scalping specific settings
        self.min_spread_ratio = 0.001  # Max 0.1% spread
        self.min_volume_24h = 1000000  # Minimum $1M daily volume
        self.profit_target = Decimal('0.3')  # 0.3% profit target
        self.stop_loss_tight = Decimal('0.15')  # 0.15% stop loss
        self.max_hold_time = 300  # 5 minutes max hold time
        self.min_price_movement = Decimal('0.05')  # 0.05% minimum movement
        
    async def analyze(self, symbol: str, market_data: Dict) -> TradingSignal:
        """Analyze market for scalping opportunities"""
        try:
            # Check basic requirements first
            if not await self._check_scalping_requirements(market_data):
                return TradingSignal(
                    signal=SignalType.HOLD,
                    confidence=0.0,
                    price=Decimal(str(market_data['price'])),
                    reasoning="Market conditions not suitable for scalping"
                )
            
            # Get 1-minute technical data
            technical_data = await self.technical_analyzer.analyze(symbol, market_data, "1m")
            
            current_price = Decimal(str(market_data['price']))
            
            # Scalping signals
            momentum_signal = await self._analyze_short_momentum(technical_data)
            orderbook_signal = await self._analyze_orderbook(market_data)
            volatility_signal = await self._analyze_volatility(market_data)
            
            # Combine signals
            combined_score = self._combine_scalping_signals(
                momentum_signal, orderbook_signal, volatility_signal
            )
            
            # Generate signal
            if combined_score > 0.8:  # High threshold for scalping
                signal_type = SignalType.BUY
                confidence = combined_score
                
                # Tight stop loss and quick profit target
                stop_loss = current_price * (1 - self.stop_loss_tight / 100)
                take_profit = current_price * (1 + self.profit_target / 100)
                
                reasoning = f"Scalping BUY: Momentum={momentum_signal:.2f}, OrderBook={orderbook_signal:.2f}, Volatility={volatility_signal:.2f}"
                
            elif combined_score < -0.8:
                signal_type = SignalType.SELL
                confidence = abs(combined_score)
                
                stop_loss = current_price * (1 + self.stop_loss_tight / 100)
                take_profit = current_price * (1 - self.profit_target / 100)
                
                reasoning = f"Scalping SELL: Momentum={momentum_signal:.2f}, OrderBook={orderbook_signal:.2f}, Volatility={volatility_signal:.2f}"
                
            else:
                signal_type = SignalType.HOLD
                confidence = 0.5
                stop_loss = None
                take_profit = None
                reasoning = "No clear scalping opportunity"
            
            return TradingSignal(
                signal=signal_type,
                confidence=confidence,
                price=current_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                reasoning=reasoning
            )
            
        except Exception as e:
            logger.error(f"Error in scalping analysis for {symbol}: {e}")
            return TradingSignal(
                signal=SignalType.HOLD,
                confidence=0.0,
                price=Decimal(str(market_data['price'])),
                reasoning=f"Scalping analysis error: {str(e)}"
            )
    
    async def _check_scalping_requirements(self, market_data: Dict) -> bool:
        """Check if market conditions are suitable for scalping"""
        try:
            # Check volume
            volume_24h = market_data.get('volume_24h_usd', 0)
            if volume_24h < self.min_volume_24h:
                return False
            
            # Check spread
            bid = Decimal(str(market_data.get('bid', 0)))
            ask = Decimal(str(market_data.get('ask', 0)))
            
            if bid > 0 and ask > 0:
                spread = (ask - bid) / bid
                if spread > self.min_spread_ratio:
                    return False
            
            # Check recent volatility
            price_change_1h = abs(market_data.get('price_change_1h', 0))
            if price_change_1h < float(self.min_price_movement):
                return False
            
            return True
            
        except Exception:
            return False
    
    async def _analyze_short_momentum(self, technical_data: Dict) -> float:
        """Analyze very short-term momentum"""
        try:
            # Use faster indicators for scalping
            rsi_fast = technical_data.get('rsi_fast', 50)  # 5-period RSI
            ema_cross = technical_data.get('ema_cross_signal', 0)  # EMA 5/10 cross
            price_velocity = technical_data.get('price_velocity', 0)  # Price change rate
            
            momentum_score = 0.0
            
            # Fast RSI (more sensitive)
            if 40 < rsi_fast < 60:  # Neutral zone
                if rsi_fast > 52:
                    momentum_score += 0.3
                elif rsi_fast < 48:
                    momentum_score -= 0.3
            elif rsi_fast > 70:  # Overbought
                momentum_score -= 0.5
            elif rsi_fast < 30:  # Oversold
                momentum_score += 0.5
            
            # EMA crossover
            momentum_score += ema_cross * 0.4
            
            # Price velocity
            momentum_score += price_velocity * 0.3
            
            return max(-1.0, min(1.0, momentum_score))
            
        except Exception:
            return 0.0
    
    async def _analyze_orderbook(self, market_data: Dict) -> float:
        """Analyze order book for scalping signals"""
        try:
            # Simplified order book analysis
            bid = Decimal(str(market_data.get('bid', 0)))
            ask = Decimal(str(market_data.get('ask', 0)))
            bid_volume = market_data.get('bid_volume', 0)
            ask_volume = market_data.get('ask_volume', 0)
            
            if bid_volume == 0 and ask_volume == 0:
                return 0.0
            
            # Volume imbalance
            total_volume = bid_volume + ask_volume
            if total_volume > 0:
                bid_ratio = bid_volume / total_volume
                
                # Strong bid = bullish, strong ask = bearish
                if bid_ratio > 0.6:
                    return 0.5
                elif bid_ratio < 0.4:
                    return -0.5
            
            return 0.0
            
        except Exception:
            return 0.0
    
    async def _analyze_volatility(self, market_data: Dict) -> float:
        """Analyze volatility for scalping"""
        try:
            # Recent price movements
            price_changes = [
                market_data.get('price_change_5m', 0),
                market_data.get('price_change_15m', 0),
                market_data.get('price_change_1h', 0)
            ]
            
            # Calculate volatility score
            volatility = sum(abs(change) for change in price_changes) / len(price_changes)
            
            # Optimal volatility range for scalping (0.1% - 1%)
            if 0.1 <= volatility <= 1.0:
                return 0.5  # Good volatility
            elif volatility > 1.0:
                return -0.3  # Too volatile
            else:
                return -0.5  # Not enough movement
                
        except Exception:
            return 0.0
    
    def _combine_scalping_signals(self, momentum: float, orderbook: float, volatility: float) -> float:
        """Combine scalping signals"""
        weights = {
            'momentum': 0.5,
            'orderbook': 0.3,
            'volatility': 0.2
        }
        
        combined = (
            weights['momentum'] * momentum +
            weights['orderbook'] * orderbook +
            weights['volatility'] * volatility
        )
        
        return max(-1.0, min(1.0, combined))
    
    async def should_enter_position(self, signal: TradingSignal, portfolio_value: Decimal) -> bool:
        """Determine if we should enter a scalping position"""
        
        # Scalping requires very high confidence
        if signal.confidence < 0.8:
            return False
        
        # Limit concurrent scalping positions
        if len(self.positions) >= 2:  # Max 2 scalping positions
            return False
        
        # Check if signal is actionable
        if signal.signal in [SignalType.BUY, SignalType.SELL]:
            return True
        
        return False
    
    async def should_exit_position(self, position: Position, current_price: Decimal) -> bool:
        """Determine if we should exit a scalping position"""
        
        # Check time-based exit (max hold time)
        current_time = time.time()
        if current_time - position.timestamp > self.max_hold_time:
            return True
        
        # Check stop loss (tight)
        if position.stop_loss:
            if position.side == 'long' and current_price <= position.stop_loss:
                return True
            elif position.side == 'short' and current_price >= position.stop_loss:
                return True
        
        # Check take profit (quick)
        if position.take_profit:
            if position.side == 'long' and current_price >= position.take_profit:
                return True
            elif position.side == 'short' and current_price <= position.take_profit:
                return True
        
        # Quick exit on small profits (scalping mentality)
        pnl, pnl_percentage = await self.calculate_pnl(position, current_price)
        
        if pnl_percentage > 0.2:  # Exit on 0.2% profit
            return True
        
        return False
    
    async def calculate_position_size(self, signal: TradingSignal, portfolio_value: Decimal) -> Decimal:
        """Calculate position size for scalping (smaller sizes)"""
        # Use smaller position sizes for scalping
        risk_amount = portfolio_value * (self.config.risk_percentage / 200)  # Half normal risk
        
        if signal.stop_loss:
            price_diff = abs(signal.price - signal.stop_loss)
            if price_diff > 0:
                position_size = risk_amount / price_diff
                return min(position_size, portfolio_value * Decimal('0.05'))  # Max 5% of portfolio
        
        return portfolio_value * (self.config.risk_percentage / 200) / signal.price
