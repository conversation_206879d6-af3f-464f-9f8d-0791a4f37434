{"$schema": "http://json-schema.org/draft-07/schema#", "python.analysis.extraPaths": ["telegram_utils"], "augment.advanced": {"useLocal": true, "local": {"provider": "openai", "apiBase": "http://127.0.0.1:1234/v1", "apiKey": "lm-studio", "model": "deepseek-coder-33b-instruct", "features": {"chat": true, "autocomplete": true}, "modelRouting": {"python": "deepseek-coder-33b-instruct", "markdown": "mistral-7b-instruct", "default": "deepseek-coder-33b-instruct"}}}, "python.formatting.provider": "ruff", "editor.formatOnSave": true, "python.linting.enabled": true, "python.linting.ruffEnabled": true, "ruff.lint.fixOnSave": true, "terminal.integrated.env.osx": {"VIRTUAL_ENV": "${workspaceFolder}/.venv", "PATH": "${workspaceFolder}/.venv/bin:${env:PATH}"}}