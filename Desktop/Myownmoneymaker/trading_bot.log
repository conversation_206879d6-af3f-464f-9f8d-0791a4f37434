2025-05-26 05:57:59 | INFO     | __main__:main:33 - Starting Telegram Trading Bot...
2025-05-26 05:57:59 | INFO     | exchanges.manager:_initialize_exchanges:30 - KuCoin exchange initialized
2025-05-26 05:57:59 | INFO     | exchanges.manager:_initialize_exchanges:41 - MEXC exchange initialized
2025-05-26 05:57:59 | INFO     | __main__:main:40 - Connecting to exchanges...
2025-05-26 05:58:02 | INFO     | exchanges.kucoin:connect:30 - Successfully connected to KuCoin
2025-05-26 05:58:11 | INFO     | exchanges.mexc:connect:29 - Successfully connected to MEXC
2025-05-26 05:58:11 | INFO     | __main__:main:45 - ✅ Connected to kucoin
2025-05-26 05:58:11 | INFO     | __main__:main:45 - ✅ Connected to mexc
2025-05-26 05:58:11 | INFO     | strategies.manager:_initialize_strategies:91 - ✅ Initialized 4 trading strategies
2025-05-26 05:58:11 | ERROR    | __main__:main:114 - Error starting bot: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
2025-05-26 05:58:11 | ERROR    | __main__:<module>:123 - Fatal error: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
2025-05-26 05:58:50 | INFO     | __main__:main:33 - Starting Telegram Trading Bot...
2025-05-26 05:58:50 | INFO     | exchanges.manager:_initialize_exchanges:30 - KuCoin exchange initialized
2025-05-26 05:58:50 | INFO     | exchanges.manager:_initialize_exchanges:41 - MEXC exchange initialized
2025-05-26 05:58:50 | INFO     | __main__:main:40 - Connecting to exchanges...
2025-05-26 05:58:52 | INFO     | exchanges.kucoin:connect:30 - Successfully connected to KuCoin
2025-05-26 05:59:01 | INFO     | exchanges.mexc:connect:29 - Successfully connected to MEXC
2025-05-26 05:59:01 | INFO     | __main__:main:45 - ✅ Connected to kucoin
2025-05-26 05:59:01 | INFO     | __main__:main:45 - ✅ Connected to mexc
2025-05-26 05:59:01 | INFO     | strategies.manager:_initialize_strategies:91 - ✅ Initialized 4 trading strategies
2025-05-26 05:59:01 | ERROR    | __main__:main:114 - Error starting bot: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
2025-05-26 05:59:01 | ERROR    | __main__:<module>:123 - Fatal error: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
2025-05-26 06:00:22 | INFO     | __main__:main:33 - Starting Telegram Trading Bot...
2025-05-26 06:00:22 | INFO     | exchanges.manager:_initialize_exchanges:30 - KuCoin exchange initialized
2025-05-26 06:00:22 | INFO     | exchanges.manager:_initialize_exchanges:41 - MEXC exchange initialized
2025-05-26 06:00:22 | INFO     | __main__:main:40 - Connecting to exchanges...
2025-05-26 06:00:24 | INFO     | exchanges.kucoin:connect:30 - Successfully connected to KuCoin
2025-05-26 06:00:30 | INFO     | exchanges.mexc:connect:29 - Successfully connected to MEXC
2025-05-26 06:00:30 | INFO     | __main__:main:45 - ✅ Connected to kucoin
2025-05-26 06:00:30 | INFO     | __main__:main:45 - ✅ Connected to mexc
2025-05-26 06:00:30 | INFO     | strategies.manager:_initialize_strategies:91 - ✅ Initialized 4 trading strategies
2025-05-26 06:00:30 | ERROR    | __main__:main:123 - Error starting bot: Only timezones from the pytz library are supported
2025-05-26 06:00:30 | ERROR    | __main__:<module>:132 - Fatal error: Only timezones from the pytz library are supported
2025-05-26 04:11:30.998 | INFO     | __main__:initialize:282 - 🚀 Initializing Telegram Trading Bot...
2025-05-26 04:11:30.999 | INFO     | exchanges.manager:_initialize_exchanges:30 - KuCoin exchange initialized
2025-05-26 04:11:30.999 | INFO     | exchanges.manager:_initialize_exchanges:41 - MEXC exchange initialized
2025-05-26 04:11:30.999 | INFO     | __main__:initialize:293 - Connecting to exchanges...
2025-05-26 04:11:34.215 | INFO     | exchanges.kucoin:connect:30 - Successfully connected to KuCoin
2025-05-26 04:11:40.330 | INFO     | exchanges.mexc:connect:29 - Successfully connected to MEXC
2025-05-26 04:11:40.330 | INFO     | __main__:initialize:298 - ✅ Connected to kucoin
2025-05-26 04:11:40.330 | INFO     | __main__:initialize:298 - ✅ Connected to mexc
2025-05-26 04:11:40.331 | INFO     | strategies.manager:_initialize_strategies:91 - ✅ Initialized 4 trading strategies
2025-05-26 04:11:40.331 | INFO     | __main__:initialize:312 - ✅ All components initialized
2025-05-26 04:11:40.345 | ERROR    | __main__:run:359 - Error running Telegram bot: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
2025-05-26 04:11:40.345 | INFO     | __main__:run:360 - 💡 Try using simple_bot.py instead for console interface
