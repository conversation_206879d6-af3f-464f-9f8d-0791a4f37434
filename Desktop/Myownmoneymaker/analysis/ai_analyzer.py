"""
AI-powered Market Analysis Module
"""
import asyncio
import json
import aiohttp
from typing import Dict, List, Optional
from decimal import Decimal
import numpy as np
from loguru import logger
from config import get_settings

class AIAnalyzer:
    """AI-powered market analysis using multiple models and data sources"""
    
    def __init__(self):
        self.settings = get_settings()
        self.session = None
    
    async def analyze_market(self, symbol: str, market_data: Dict) -> Dict:
        """Comprehensive AI market analysis"""
        try:
            # Sentiment analysis
            sentiment = await self.analyze_sentiment(symbol, market_data)
            
            # Trend analysis
            trend = await self.analyze_trend(symbol, market_data)
            
            # Pattern recognition
            patterns = await self.detect_patterns(symbol, market_data)
            
            # News impact analysis
            news_impact = await self.analyze_news_impact(symbol)
            
            # Social media sentiment
            social_sentiment = await self.analyze_social_sentiment(symbol)
            
            # Combine all AI analyses
            combined_analysis = {
                'sentiment': sentiment,
                'trend': trend,
                'patterns': patterns,
                'news_impact': news_impact,
                'social_sentiment': social_sentiment,
                'confidence': self._calculate_ai_confidence(sentiment, trend, patterns),
                'recommendation': self._generate_recommendation(sentiment, trend, patterns)
            }
            
            return combined_analysis
            
        except Exception as e:
            logger.error(f"Error in AI market analysis for {symbol}: {e}")
            return {
                'sentiment': {'score': 0.5, 'confidence': 0.0},
                'trend': {'direction': 'neutral', 'strength': 0.0},
                'patterns': [],
                'confidence': 0.0,
                'recommendation': 'hold'
            }
    
    async def analyze_sentiment(self, symbol: str, market_data: Dict) -> Dict:
        """Analyze market sentiment using AI"""
        try:
            # Combine multiple sentiment sources
            sentiment_sources = []
            
            # Technical sentiment
            tech_sentiment = await self._calculate_technical_sentiment(market_data)
            sentiment_sources.append(('technical', tech_sentiment, 0.4))
            
            # News sentiment
            news_sentiment = await self._analyze_news_sentiment(symbol)
            sentiment_sources.append(('news', news_sentiment, 0.3))
            
            # Social sentiment
            social_sentiment = await self._analyze_social_media_sentiment(symbol)
            sentiment_sources.append(('social', social_sentiment, 0.3))
            
            # Weighted average
            total_weight = sum(weight for _, _, weight in sentiment_sources)
            weighted_sentiment = sum(sentiment * weight for _, sentiment, weight in sentiment_sources) / total_weight
            
            # Calculate confidence based on agreement between sources
            sentiments = [sentiment for _, sentiment, _ in sentiment_sources]
            confidence = 1.0 - (np.std(sentiments) / 0.5)  # Lower std = higher confidence
            
            return {
                'score': max(0.0, min(1.0, weighted_sentiment)),
                'confidence': max(0.0, min(1.0, confidence)),
                'sources': {source: sentiment for source, sentiment, _ in sentiment_sources}
            }
            
        except Exception as e:
            logger.error(f"Error in sentiment analysis: {e}")
            return {'score': 0.5, 'confidence': 0.0}
    
    async def analyze_trend(self, symbol: str, market_data: Dict) -> Dict:
        """AI-powered trend analysis"""
        try:
            # Price momentum analysis
            price_changes = [
                market_data.get('price_change_1h', 0),
                market_data.get('price_change_4h', 0),
                market_data.get('price_change_24h', 0),
                market_data.get('price_change_7d', 0)
            ]
            
            # Calculate trend strength
            positive_changes = sum(1 for change in price_changes if change > 0)
            trend_consistency = positive_changes / len(price_changes)
            
            # Volume trend
            volume_trend = await self._analyze_volume_trend(market_data)
            
            # Determine trend direction and strength
            if trend_consistency > 0.75:
                direction = 'bullish'
                strength = trend_consistency
            elif trend_consistency < 0.25:
                direction = 'bearish'
                strength = 1 - trend_consistency
            else:
                direction = 'neutral'
                strength = 0.5
            
            # Adjust strength based on volume
            strength = (strength + volume_trend) / 2
            
            return {
                'direction': direction,
                'strength': max(0.0, min(1.0, strength)),
                'consistency': trend_consistency,
                'volume_confirmation': volume_trend > 0.5
            }
            
        except Exception as e:
            logger.error(f"Error in trend analysis: {e}")
            return {'direction': 'neutral', 'strength': 0.0}
    
    async def detect_patterns(self, symbol: str, market_data: Dict) -> List[Dict]:
        """AI pattern recognition"""
        try:
            patterns = []
            
            # Simple pattern detection (in real implementation, use ML models)
            current_price = market_data['price']
            high_24h = market_data.get('high_24h', current_price)
            low_24h = market_data.get('low_24h', current_price)
            
            # Breakout pattern
            if current_price > high_24h * 0.98:
                patterns.append({
                    'type': 'breakout_resistance',
                    'confidence': 0.7,
                    'signal': 'bullish'
                })
            
            # Support bounce pattern
            if current_price < low_24h * 1.02:
                patterns.append({
                    'type': 'support_bounce',
                    'confidence': 0.6,
                    'signal': 'bullish'
                })
            
            # Consolidation pattern
            if high_24h > 0 and low_24h > 0:
                range_size = (high_24h - low_24h) / low_24h
                if range_size < 0.05:  # Less than 5% range
                    patterns.append({
                        'type': 'consolidation',
                        'confidence': 0.8,
                        'signal': 'neutral'
                    })
            
            return patterns
            
        except Exception as e:
            logger.error(f"Error in pattern detection: {e}")
            return []
    
    async def analyze_news_impact(self, symbol: str) -> Dict:
        """Analyze news impact on price"""
        try:
            # In real implementation, fetch and analyze news
            # For now, return simulated analysis
            
            return {
                'impact_score': 0.5,  # -1 to 1
                'confidence': 0.6,
                'key_events': [],
                'sentiment': 'neutral'
            }
            
        except Exception as e:
            logger.error(f"Error in news impact analysis: {e}")
            return {'impact_score': 0.0, 'confidence': 0.0}
    
    async def analyze_social_sentiment(self, symbol: str) -> Dict:
        """Analyze social media sentiment"""
        try:
            # In real implementation, analyze Twitter, Reddit, etc.
            # For now, return simulated analysis
            
            return {
                'sentiment_score': 0.5,  # 0 to 1
                'confidence': 0.5,
                'volume': 'medium',
                'trending': False
            }
            
        except Exception as e:
            logger.error(f"Error in social sentiment analysis: {e}")
            return {'sentiment_score': 0.5, 'confidence': 0.0}
    
    async def _calculate_technical_sentiment(self, market_data: Dict) -> float:
        """Calculate sentiment from technical indicators"""
        try:
            sentiment = 0.5  # Neutral starting point
            
            # Price momentum
            price_change_24h = market_data.get('price_change_24h', 0)
            if price_change_24h > 5:
                sentiment += 0.2
            elif price_change_24h < -5:
                sentiment -= 0.2
            
            # Volume analysis
            volume = market_data.get('volume', 0)
            avg_volume = market_data.get('avg_volume_24h', volume)
            
            if avg_volume > 0:
                volume_ratio = volume / avg_volume
                if volume_ratio > 1.5:
                    sentiment += 0.1  # High volume is positive
                elif volume_ratio < 0.5:
                    sentiment -= 0.1  # Low volume is negative
            
            return max(0.0, min(1.0, sentiment))
            
        except Exception:
            return 0.5
    
    async def _analyze_news_sentiment(self, symbol: str) -> float:
        """Analyze news sentiment (placeholder)"""
        try:
            # In real implementation, use news APIs and NLP
            return 0.5  # Neutral
            
        except Exception:
            return 0.5
    
    async def _analyze_social_media_sentiment(self, symbol: str) -> float:
        """Analyze social media sentiment (placeholder)"""
        try:
            # In real implementation, analyze Twitter, Reddit, etc.
            return 0.5  # Neutral
            
        except Exception:
            return 0.5
    
    async def _analyze_volume_trend(self, market_data: Dict) -> float:
        """Analyze volume trend"""
        try:
            current_volume = market_data.get('volume', 0)
            avg_volume = market_data.get('avg_volume_24h', current_volume)
            
            if avg_volume == 0:
                return 0.5
            
            volume_ratio = current_volume / avg_volume
            
            # Convert to 0-1 scale
            if volume_ratio > 2:
                return 1.0
            elif volume_ratio > 1.5:
                return 0.8
            elif volume_ratio > 1:
                return 0.6
            elif volume_ratio > 0.5:
                return 0.4
            else:
                return 0.2
                
        except Exception:
            return 0.5
    
    def _calculate_ai_confidence(self, sentiment: Dict, trend: Dict, patterns: List) -> float:
        """Calculate overall AI confidence"""
        try:
            confidence_factors = []
            
            # Sentiment confidence
            confidence_factors.append(sentiment.get('confidence', 0.0))
            
            # Trend confidence (based on strength)
            confidence_factors.append(trend.get('strength', 0.0))
            
            # Pattern confidence
            if patterns:
                pattern_confidence = np.mean([p.get('confidence', 0.0) for p in patterns])
                confidence_factors.append(pattern_confidence)
            else:
                confidence_factors.append(0.3)  # Lower confidence without patterns
            
            # Average confidence
            return np.mean(confidence_factors)
            
        except Exception:
            return 0.0
    
    def _generate_recommendation(self, sentiment: Dict, trend: Dict, patterns: List) -> str:
        """Generate AI recommendation"""
        try:
            sentiment_score = sentiment.get('score', 0.5)
            trend_direction = trend.get('direction', 'neutral')
            trend_strength = trend.get('strength', 0.0)
            
            # Strong bullish signals
            if sentiment_score > 0.7 and trend_direction == 'bullish' and trend_strength > 0.7:
                return 'strong_buy'
            
            # Moderate bullish signals
            elif sentiment_score > 0.6 and trend_direction == 'bullish':
                return 'buy'
            
            # Strong bearish signals
            elif sentiment_score < 0.3 and trend_direction == 'bearish' and trend_strength > 0.7:
                return 'strong_sell'
            
            # Moderate bearish signals
            elif sentiment_score < 0.4 and trend_direction == 'bearish':
                return 'sell'
            
            # Default to hold
            else:
                return 'hold'
                
        except Exception:
            return 'hold'
