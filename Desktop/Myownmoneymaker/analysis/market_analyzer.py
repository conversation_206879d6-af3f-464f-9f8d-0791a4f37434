"""
Automated Market Analysis - Runs every 5 minutes
"""
import asyncio
import time
from typing import Dict, List
from datetime import datetime, timedelta
from decimal import Decimal
from loguru import logger
import pandas as pd
import pandas_ta as ta
from .technical_simple import TechnicalAnalyzer
from .ai_analyzer import AIAnalyzer
from exchanges.manager import ExchangeManager
from config import get_settings

class MarketAnalyzer:
    """Automated market analysis that runs every 5 minutes"""

    def __init__(self, exchange_manager: ExchangeManager):
        self.exchange_manager = exchange_manager
        self.technical_analyzer = TechnicalAnalyzer()
        self.ai_analyzer = AIAnalyzer()
        self.settings = get_settings()

        # Analysis settings
        self.analysis_interval = 300  # 5 minutes in seconds
        self.symbols_to_analyze = ["BTC/USDT", "ETH/USDT", "BNB/USDT", "ADA/USDT", "SOL/USDT"]
        self.running = False

        # Store analysis results
        self.latest_analysis = {}
        self.analysis_history = {}

        # Alert thresholds
        self.price_change_alert_threshold = 5.0  # 5% price change
        self.volume_spike_threshold = 3.0  # 3x normal volume
        self.volatility_alert_threshold = 10.0  # 10% volatility

        # Augment: fine-tuned AI settings for optimal trading bot coding
        # (Pas aan naar wens, zie settings.json voor meer opties)
        # Voorbeeld: self.augment_features = {"chat": True, "autocomplete": True, "codeSearch": True, "docSearch": True}
        self.augment_features = {
            "chat": True,  # Snel vragen stellen over code of trading
            "autocomplete": True,  # Inline Python aanvulling
            "codeSearch": False,  # Alleen aanzetten als je veel zoekt
            "docSearch": True,  # Direct uitleg uit docstrings/README
            "modelRouting": {
                "python": "deepseek-coder-33b-instruct",
                "markdown": "mistral-7b-instruct",
                "default": "deepseek-coder-33b-instruct"
            },
            "temperature": 0.2,  # Veilige, voorspelbare code
            "max_context_length": 2048  # Genoeg context voor trading scripts
        }

    async def start_analysis(self):
        """Start the automated market analysis"""
        self.running = True
        logger.info("🔍 Starting automated market analysis (every 5 minutes)")

        while self.running:
            try:
                start_time = time.time()

                # Perform analysis for all symbols
                await self._perform_market_analysis()

                # Check for alerts
                await self._check_market_alerts()

                # Calculate next run time
                elapsed_time = time.time() - start_time
                sleep_time = max(0, self.analysis_interval - elapsed_time)

                logger.info(f"📊 Market analysis completed in {elapsed_time:.2f}s. Next analysis in {sleep_time:.0f}s")

                if self.running:
                    await asyncio.sleep(sleep_time)

            except Exception as e:
                logger.error(f"Error in market analysis loop: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retrying

    def stop_analysis(self):
        """Stop the automated market analysis"""
        self.running = False
        logger.info("🛑 Stopping automated market analysis")

    async def _perform_market_analysis(self):
        """Perform comprehensive market analysis"""
        analysis_timestamp = datetime.now()

        for symbol in self.symbols_to_analyze:
            try:
                # Get market data from all exchanges
                market_data = await self._get_comprehensive_market_data(symbol)

                if not market_data:
                    continue

                # Get historical data for advanced indicators
                historical_data = await self._get_historical_data(symbol)

                # Technical analysis
                technical_analysis = await self.technical_analyzer.analyze(symbol, market_data)

                # Add advanced indicators if historical data is available
                if not historical_data.empty:
                    latest_data = historical_data.iloc[-1]
                    technical_analysis['adx'] = latest_data.get('adx', 0)
                    technical_analysis['bb_width'] = latest_data.get('bb_width', 0)

                    # Add ADX interpretation
                    adx_value = latest_data.get('adx', 0)
                    if adx_value > 25:
                        technical_analysis['adx_signal'] = 'strong_trend'
                    elif adx_value > 20:
                        technical_analysis['adx_signal'] = 'trending'
                    else:
                        technical_analysis['adx_signal'] = 'sideways'

                    # Add Bollinger Bands width interpretation
                    bb_width = latest_data.get('bb_width', 0)
                    if bb_width > 0.1:
                        technical_analysis['bb_signal'] = 'high_volatility'
                    elif bb_width < 0.05:
                        technical_analysis['bb_signal'] = 'low_volatility'
                    else:
                        technical_analysis['bb_signal'] = 'normal_volatility'

                # AI analysis
                ai_analysis = await self.ai_analyzer.analyze_market(symbol, market_data)

                # Combine analyses
                combined_analysis = {
                    'timestamp': analysis_timestamp,
                    'symbol': symbol,
                    'market_data': market_data,
                    'technical': technical_analysis,
                    'ai': ai_analysis,
                    'alerts': await self._generate_alerts(symbol, market_data, technical_analysis, ai_analysis)
                }

                # Store results
                self.latest_analysis[symbol] = combined_analysis

                # Store in history
                if symbol not in self.analysis_history:
                    self.analysis_history[symbol] = []

                self.analysis_history[symbol].append(combined_analysis)

                # Keep only last 100 analyses per symbol
                if len(self.analysis_history[symbol]) > 100:
                    self.analysis_history[symbol] = self.analysis_history[symbol][-100:]

                logger.debug(f"✅ Analysis completed for {symbol}")

            except Exception as e:
                logger.error(f"Error analyzing {symbol}: {e}")

    async def _get_comprehensive_market_data(self, symbol: str) -> Dict:
        """Get comprehensive market data from all exchanges"""
        try:
            # Get tickers from all exchanges
            tickers = await self.exchange_manager.get_ticker_from_all(symbol)

            if not tickers:
                return {}

            # Use the exchange with the highest volume
            best_ticker = None
            highest_volume = 0

            for exchange_name, ticker in tickers.items():
                if ticker.volume > highest_volume:
                    highest_volume = ticker.volume
                    best_ticker = ticker

            if not best_ticker:
                return {}

            # Create comprehensive market data
            market_data = {
                'symbol': symbol,
                'price': float(best_ticker.last),
                'bid': float(best_ticker.bid),
                'ask': float(best_ticker.ask),
                'high_24h': float(best_ticker.high),
                'low_24h': float(best_ticker.low),
                'volume': float(best_ticker.volume),
                'timestamp': best_ticker.timestamp,

                # Calculate additional metrics
                'spread': float((best_ticker.ask - best_ticker.bid) / best_ticker.bid * 100) if best_ticker.bid > 0 else 0,
                'price_change_24h': float((best_ticker.last - best_ticker.low) / best_ticker.low * 100) if best_ticker.low > 0 else 0,

                # Exchange comparison
                'exchange_prices': {name: float(ticker.last) for name, ticker in tickers.items()},
                'price_variance': self._calculate_price_variance(tickers),

                # Volume analysis
                'avg_volume_24h': float(best_ticker.volume),  # Simplified
                'volume_24h_usd': float(best_ticker.volume * best_ticker.last),
            }

            return market_data

        except Exception as e:
            logger.error(f"Error getting market data for {symbol}: {e}")
            return {}

    def _calculate_price_variance(self, tickers: Dict) -> float:
        """Calculate price variance across exchanges"""
        try:
            prices = [float(ticker.last) for ticker in tickers.values()]
            if len(prices) < 2:
                return 0.0

            avg_price = sum(prices) / len(prices)
            variance = sum((price - avg_price) ** 2 for price in prices) / len(prices)

            return (variance ** 0.5) / avg_price * 100  # Coefficient of variation as percentage

        except Exception:
            return 0.0

    def _calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate ADX and Bollinger Bands width indicators"""
        try:
            # Calculate ADX (Average Directional Index)
            df['adx'] = ta.adx(df['high'], df['low'], df['close'])['ADX_14']

            # Calculate Bollinger Bands and width
            bb = ta.bbands(df['close'], length=20, std=2)
            df['bb_width'] = (bb['BBU_20_2.0'] - bb['BBL_20_2.0']) / bb['BBM_20_2.0']

            return df

        except Exception as e:
            logger.error(f"Error calculating indicators: {e}")
            return df

    async def _get_historical_data(self, symbol: str, timeframe: str = '1h', limit: int = 100) -> pd.DataFrame:
        """Get historical OHLCV data for technical analysis"""
        try:
            # Try to get historical data from the first available exchange
            for exchange_name, exchange in self.exchange_manager.exchanges.items():
                if hasattr(exchange, 'fetch_ohlcv'):
                    try:
                        ohlcv = await exchange.fetch_ohlcv(symbol, timeframe, limit=limit)

                        if ohlcv:
                            # Convert to DataFrame
                            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                            df.set_index('timestamp', inplace=True)

                            # Calculate indicators
                            df = self._calculate_indicators(df)

                            return df

                    except Exception as e:
                        logger.debug(f"Failed to get historical data from {exchange_name}: {e}")
                        continue

            # If no historical data available, create empty DataFrame
            logger.warning(f"No historical data available for {symbol}")
            return pd.DataFrame()

        except Exception as e:
            logger.error(f"Error getting historical data for {symbol}: {e}")
            return pd.DataFrame()

    async def _generate_alerts(self, symbol: str, market_data: Dict,
                             technical_analysis: Dict, ai_analysis: Dict) -> List[Dict]:
        """Generate market alerts based on analysis"""
        alerts = []

        try:
            # Price change alerts
            price_change = abs(market_data.get('price_change_24h', 0))
            if price_change > self.price_change_alert_threshold:
                alerts.append({
                    'type': 'price_movement',
                    'severity': 'high' if price_change > 10 else 'medium',
                    'message': f"{symbol} moved {price_change:.2f}% in 24h",
                    'value': price_change
                })

            # Volume spike alerts
            volume = market_data.get('volume', 0)
            avg_volume = market_data.get('avg_volume_24h', volume)

            if avg_volume > 0:
                volume_ratio = volume / avg_volume
                if volume_ratio > self.volume_spike_threshold:
                    alerts.append({
                        'type': 'volume_spike',
                        'severity': 'high' if volume_ratio > 5 else 'medium',
                        'message': f"{symbol} volume is {volume_ratio:.1f}x normal",
                        'value': volume_ratio
                    })

            # Technical alerts
            if 'rsi' in technical_analysis:
                rsi = technical_analysis['rsi']
                if rsi > 80:
                    alerts.append({
                        'type': 'technical',
                        'severity': 'medium',
                        'message': f"{symbol} RSI extremely overbought ({rsi:.1f})",
                        'value': rsi
                    })
                elif rsi < 20:
                    alerts.append({
                        'type': 'technical',
                        'severity': 'medium',
                        'message': f"{symbol} RSI extremely oversold ({rsi:.1f})",
                        'value': rsi
                    })

            # ADX alerts
            if 'adx' in technical_analysis:
                adx = technical_analysis['adx']
                adx_signal = technical_analysis.get('adx_signal', '')

                if adx > 30 and adx_signal == 'strong_trend':
                    alerts.append({
                        'type': 'technical',
                        'severity': 'medium',
                        'message': f"{symbol} ADX shows strong trend ({adx:.1f})",
                        'value': adx
                    })
                elif adx < 15:
                    alerts.append({
                        'type': 'technical',
                        'severity': 'low',
                        'message': f"{symbol} ADX shows weak trend/sideways ({adx:.1f})",
                        'value': adx
                    })

            # Bollinger Bands width alerts
            if 'bb_width' in technical_analysis:
                bb_width = technical_analysis['bb_width']
                bb_signal = technical_analysis.get('bb_signal', '')

                if bb_signal == 'low_volatility':
                    alerts.append({
                        'type': 'technical',
                        'severity': 'medium',
                        'message': f"{symbol} Bollinger Bands squeeze - potential breakout ({bb_width:.4f})",
                        'value': bb_width
                    })
                elif bb_signal == 'high_volatility':
                    alerts.append({
                        'type': 'technical',
                        'severity': 'medium',
                        'message': f"{symbol} High volatility detected ({bb_width:.4f})",
                        'value': bb_width
                    })

            # AI alerts
            ai_confidence = ai_analysis.get('confidence', 0)
            ai_recommendation = ai_analysis.get('recommendation', 'hold')

            if ai_confidence > 0.8 and ai_recommendation in ['strong_buy', 'strong_sell']:
                alerts.append({
                    'type': 'ai_signal',
                    'severity': 'high',
                    'message': f"{symbol} AI recommends {ai_recommendation} (confidence: {ai_confidence:.2f})",
                    'value': ai_confidence
                })

            # Price variance alert (arbitrage opportunity)
            price_variance = market_data.get('price_variance', 0)
            if price_variance > 2.0:  # More than 2% variance between exchanges
                alerts.append({
                    'type': 'arbitrage',
                    'severity': 'medium',
                    'message': f"{symbol} price variance {price_variance:.2f}% across exchanges",
                    'value': price_variance
                })

            return alerts

        except Exception as e:
            logger.error(f"Error generating alerts for {symbol}: {e}")
            return []

    async def _check_market_alerts(self):
        """Check and log market alerts"""
        try:
            all_alerts = []

            for symbol, analysis in self.latest_analysis.items():
                alerts = analysis.get('alerts', [])
                for alert in alerts:
                    alert['symbol'] = symbol
                    all_alerts.append(alert)

            # Sort by severity
            severity_order = {'high': 3, 'medium': 2, 'low': 1}
            all_alerts.sort(key=lambda x: severity_order.get(x.get('severity', 'low'), 1), reverse=True)

            # Log alerts
            if all_alerts:
                logger.info(f"🚨 {len(all_alerts)} market alerts detected:")
                for alert in all_alerts[:10]:  # Show top 10 alerts
                    severity_emoji = {'high': '🔴', 'medium': '🟡', 'low': '🟢'}.get(alert.get('severity', 'low'), '⚪')
                    logger.info(f"  {severity_emoji} {alert['message']}")

        except Exception as e:
            logger.error(f"Error checking market alerts: {e}")

    def get_latest_analysis(self, symbol: str = None) -> Dict:
        """Get latest analysis for a symbol or all symbols"""
        if symbol:
            return self.latest_analysis.get(symbol, {})
        return self.latest_analysis

    def get_analysis_summary(self) -> Dict:
        """Get summary of latest market analysis"""
        try:
            summary = {
                'timestamp': datetime.now(),
                'symbols_analyzed': len(self.latest_analysis),
                'total_alerts': 0,
                'high_severity_alerts': 0,
                'market_overview': {}
            }

            for symbol, analysis in self.latest_analysis.items():
                alerts = analysis.get('alerts', [])
                summary['total_alerts'] += len(alerts)
                summary['high_severity_alerts'] += len([a for a in alerts if a.get('severity') == 'high'])

                # Market overview
                market_data = analysis.get('market_data', {})
                ai_analysis = analysis.get('ai', {})

                summary['market_overview'][symbol] = {
                    'price': market_data.get('price', 0),
                    'price_change_24h': market_data.get('price_change_24h', 0),
                    'ai_recommendation': ai_analysis.get('recommendation', 'hold'),
                    'ai_confidence': ai_analysis.get('confidence', 0),
                    'alerts': len(alerts)
                }

            return summary

        except Exception as e:
            logger.error(f"Error generating analysis summary: {e}")
            return {}

    async def get_market_report(self) -> str:
        """Generate a formatted market report"""
        try:
            summary = self.get_analysis_summary()

            report = f"📊 **Market Analysis Report**\n"
            report += f"🕐 {summary['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}\n\n"

            report += f"📈 **Overview:**\n"
            report += f"• Symbols analyzed: {summary['symbols_analyzed']}\n"
            report += f"• Total alerts: {summary['total_alerts']}\n"
            report += f"• High priority alerts: {summary['high_severity_alerts']}\n\n"

            report += f"💰 **Market Status:**\n"

            for symbol, data in summary['market_overview'].items():
                price_change = data['price_change_24h']
                change_emoji = "📈" if price_change > 0 else "📉" if price_change < 0 else "➡️"

                report += f"{change_emoji} **{symbol}**: ${data['price']:.4f} "
                report += f"({price_change:+.2f}%)\n"
                report += f"   AI: {data['ai_recommendation']} ({data['ai_confidence']:.2f})\n"

                if data['alerts'] > 0:
                    report += f"   🚨 {data['alerts']} alerts\n"
                report += "\n"

            return report

        except Exception as e:
            logger.error(f"Error generating market report: {e}")
            return "❌ Error generating market report"
